#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Deploying ChewyAI to Netlify..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Parse command line arguments
PRODUCTION_DEPLOY=false
SITE_ID=""
BUILD_COMMAND=""
PUBLISH_DIR=""
SKIP_BUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --prod|--production)
            PRODUCTION_DEPLOY=true
            shift
            ;;
        --site-id)
            SITE_ID="$2"
            shift 2
            ;;
        --build-command)
            BUILD_COMMAND="$2"
            shift 2
            ;;
        --publish-dir)
            PUBLISH_DIR="$2"
            shift 2
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --prod, --production    Deploy to production (default: preview)"
            echo "  --site-id <id>          Specify Netlify site ID"
            echo "  --build-command <cmd>   Override build command"
            echo "  --publish-dir <dir>     Override publish directory"
            echo "  --skip-build           Skip build step"
            echo "  -h, --help             Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                     # Deploy preview"
            echo "  $0 --prod              # Deploy to production"
            echo "  $0 --site-id abc123    # Deploy to specific site"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if Netlify CLI is installed
print_status "Checking Netlify CLI..."
if ! command -v netlify >/dev/null 2>&1; then
    print_error "Netlify CLI is not installed or not in PATH"
    print_status "Install it with: npm install -g netlify-cli"
    exit 1
fi

print_success "Netlify CLI $(netlify --version) is available"

# Check if user is logged in to Netlify
print_status "Checking Netlify authentication..."
if ! netlify status >/dev/null 2>&1; then
    print_warning "Not logged in to Netlify"
    print_status "Logging in to Netlify..."
    netlify login
fi

print_success "Authenticated with Netlify"

# Set default values based on project structure
if [ -z "$BUILD_COMMAND" ]; then
    BUILD_COMMAND="npm run build:frontend"
fi

if [ -z "$PUBLISH_DIR" ]; then
    PUBLISH_DIR="frontend/dist"
fi

# Check if this is a linked site
if [ -z "$SITE_ID" ] && [ -f ".netlify/state.json" ]; then
    print_status "Found existing Netlify site configuration"
    SITE_ID=$(cat .netlify/state.json | grep -o '"siteId":"[^"]*"' | cut -d'"' -f4 || echo "")
fi

# If no site ID, check if we need to create or link a site
if [ -z "$SITE_ID" ]; then
    print_warning "No Netlify site configured"
    echo ""
    echo "Options:"
    echo "1. Link to existing site: netlify link"
    echo "2. Create new site: netlify init"
    echo "3. Specify site ID with --site-id option"
    echo ""
    read -p "Would you like to link to an existing site? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        netlify link
    else
        print_status "Creating new Netlify site..."
        netlify init
    fi
fi

# Build the project if not skipping
if [ "$SKIP_BUILD" = false ]; then
    print_status "Building project for deployment..."
    
    # Check dependencies
    if [ ! -d "frontend/node_modules" ]; then
        print_warning "Frontend dependencies not found. Installing..."
        cd frontend && npm install && cd ..
    fi
    
    # Clean previous build
    print_status "Cleaning previous build..."
    rm -rf frontend/dist
    
    # Run build command
    print_status "Running build command: $BUILD_COMMAND"
    eval "$BUILD_COMMAND"
    
    # Verify build output
    if [ ! -d "$PUBLISH_DIR" ]; then
        print_error "Build failed - publish directory '$PUBLISH_DIR' not found"
        exit 1
    fi
    
    if [ ! -f "$PUBLISH_DIR/index.html" ]; then
        print_error "Build failed - index.html not found in '$PUBLISH_DIR'"
        exit 1
    fi
    
    print_success "Build completed successfully"
else
    print_warning "Skipping build step"
    
    # Verify publish directory exists
    if [ ! -d "$PUBLISH_DIR" ]; then
        print_error "Publish directory '$PUBLISH_DIR' not found. Run build first or remove --skip-build flag."
        exit 1
    fi
fi

# Deploy to Netlify
print_status "Deploying to Netlify..."

DEPLOY_ARGS="--dir=$PUBLISH_DIR"

if [ -n "$SITE_ID" ]; then
    DEPLOY_ARGS="$DEPLOY_ARGS --site=$SITE_ID"
fi

if [ "$PRODUCTION_DEPLOY" = true ]; then
    print_status "Deploying to production..."
    DEPLOY_ARGS="$DEPLOY_ARGS --prod"
    
    # Confirm production deployment
    echo ""
    print_warning "You are about to deploy to PRODUCTION"
    read -p "Are you sure you want to continue? (y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Deployment cancelled"
        exit 0
    fi
else
    print_status "Deploying preview..."
fi

# Execute deployment
print_status "Executing: netlify deploy $DEPLOY_ARGS"
DEPLOY_OUTPUT=$(netlify deploy $DEPLOY_ARGS)

# Parse deployment output
DEPLOY_URL=$(echo "$DEPLOY_OUTPUT" | grep -o 'https://[^[:space:]]*' | head -1)
LIVE_URL=$(echo "$DEPLOY_OUTPUT" | grep "Live Draft URL:" | grep -o 'https://[^[:space:]]*' || echo "")

print_success "🎉 Deployment completed!"
echo ""
print_status "Deployment Summary:"
echo "  - Publish Directory: $PUBLISH_DIR"
echo "  - Build Command: $BUILD_COMMAND"

if [ "$PRODUCTION_DEPLOY" = true ]; then
    echo "  - Type: Production"
    if [ -n "$LIVE_URL" ]; then
        echo "  - Live URL: $LIVE_URL"
    elif [ -n "$DEPLOY_URL" ]; then
        echo "  - Live URL: $DEPLOY_URL"
    fi
else
    echo "  - Type: Preview"
    if [ -n "$DEPLOY_URL" ]; then
        echo "  - Preview URL: $DEPLOY_URL"
    fi
fi

echo ""
print_status "Next steps:"
if [ "$PRODUCTION_DEPLOY" = false ]; then
    echo "  - Test your preview deployment"
    echo "  - Deploy to production: bash scripts/deploy.sh --prod"
fi
echo "  - View deployment logs: netlify open"
echo "  - Manage site settings: netlify open:admin"

# Display URLs for easy access
if [ -n "$DEPLOY_URL" ]; then
    echo ""
    print_success "🌐 Your site is live at: $DEPLOY_URL"
fi
