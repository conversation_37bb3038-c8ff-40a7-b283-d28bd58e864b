# ChewyAI Deployment Guide

This guide covers deploying ChewyAI to Netlify using the provided deployment scripts.

## Prerequisites

1. **Netlify CLI**: Already installed ✅
2. **Node.js 18+**: Required for building the project
3. **Git**: For version control and deployment
4. **Netlify Account**: Sign up at [netlify.com](https://netlify.com)

## Quick Deployment

### 1. First-time Setup

```bash
# Make scripts executable (if needed)
chmod +x scripts/*.sh

# Set up the project
bash scripts/setup.sh

# Login to Netlify (if not already logged in)
netlify login
```

### 2. Deploy Preview

```bash
# Deploy a preview version
bash scripts/deploy.sh
```

### 3. Deploy to Production

```bash
# Deploy to production
bash scripts/deploy.sh --prod
```

## Deployment Script Options

The `scripts/deploy.sh` script supports several options:

```bash
# Basic usage
bash scripts/deploy.sh [OPTIONS]

# Options:
--prod, --production    # Deploy to production (default: preview)
--site-id <id>         # Specify Netlify site ID
--build-command <cmd>  # Override build command
--publish-dir <dir>    # Override publish directory
--skip-build          # Skip build step
-h, --help            # Show help message
```

### Examples

```bash
# Deploy preview
bash scripts/deploy.sh

# Deploy to production
bash scripts/deploy.sh --prod

# Deploy to specific site
bash scripts/deploy.sh --site-id abc123

# Deploy with custom build command
bash scripts/deploy.sh --build-command "npm run build"

# Deploy without rebuilding
bash scripts/deploy.sh --skip-build
```

## Project Structure for Deployment

```
chewyai/
├── frontend/           # React frontend
│   ├── dist/          # Build output (created by build)
│   ├── src/           # Source code
│   └── package.json   # Frontend dependencies
├── backend/           # Express backend (not deployed to Netlify)
│   ├── dist/          # Build output
│   └── package.json   # Backend dependencies
├── scripts/           # Deployment scripts
│   └── deploy.sh      # Main deployment script
├── netlify.toml       # Netlify configuration
└── package.json       # Root package.json
```

## Netlify Configuration

The `netlify.toml` file configures:

- **Build Settings**: Frontend build command and output directory
- **Redirects**: SPA routing and API proxy rules
- **Headers**: Security and caching headers
- **Environment**: Node.js version and environment variables

### Key Settings

```toml
[build]
  command = "npm run build:frontend"
  publish = "frontend/dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

## Environment Variables

Set environment variables in Netlify dashboard:

1. Go to **Site Settings** → **Environment Variables**
2. Add required variables:
   - `NODE_ENV=production`
   - `REACT_APP_API_URL=your-backend-url`
   - Any other environment-specific variables

## Backend Deployment

**Note**: The Netlify deployment only covers the frontend. The backend needs to be deployed separately to a service like:

- **Railway**: `railway deploy`
- **Render**: Connect GitHub repository
- **Heroku**: `git push heroku main`
- **DigitalOcean App Platform**: Connect GitHub repository

### Backend Environment Variables

Make sure your backend is deployed with:
- Database connection strings
- API keys and secrets
- CORS settings allowing your Netlify domain

## Troubleshooting

### Common Issues

1. **Build Fails**
   ```bash
   # Check dependencies
   npm install
   cd frontend && npm install
   
   # Test build locally
   npm run build:frontend
   ```

2. **Site Not Found**
   ```bash
   # Link to existing site
   netlify link
   
   # Or create new site
   netlify init
   ```

3. **Authentication Issues**
   ```bash
   # Re-login to Netlify
   netlify logout
   netlify login
   ```

4. **Build Command Errors**
   ```bash
   # Use custom build command
   bash scripts/deploy.sh --build-command "cd frontend && npm run build"
   ```

### Debug Deployment

```bash
# Check Netlify status
netlify status

# View site info
netlify sites:list

# Open site in browser
netlify open

# View deployment logs
netlify logs
```

## Continuous Deployment

### GitHub Integration

1. Connect your GitHub repository to Netlify
2. Set build settings:
   - **Build command**: `npm run build:frontend`
   - **Publish directory**: `frontend/dist`
3. Enable auto-deploy on push to main branch

### Manual Deployment

Use the deployment script for manual deployments:

```bash
# Deploy latest changes
git add .
git commit -m "Update deployment"
bash scripts/deploy.sh --prod
```

## Performance Optimization

The deployment includes:

- **Static asset caching**: 1 year cache for assets
- **HTML caching**: No cache for index.html
- **Compression**: Automatic gzip/brotli compression
- **CDN**: Global content delivery network
- **Lighthouse optimization**: Performance monitoring

## Security Features

- **HTTPS**: Automatic SSL certificates
- **Security headers**: XSS protection, content type sniffing prevention
- **CORS**: Configured for API access
- **Environment isolation**: Separate preview and production environments

## Monitoring

After deployment, monitor your site:

1. **Netlify Analytics**: Built-in traffic analytics
2. **Lighthouse Reports**: Automated performance testing
3. **Deploy Notifications**: Email/Slack notifications
4. **Error Tracking**: Monitor deployment and runtime errors

## Support

For deployment issues:

1. Check [Netlify Documentation](https://docs.netlify.com)
2. Review deployment logs in Netlify dashboard
3. Test builds locally before deploying
4. Use preview deployments to test changes
