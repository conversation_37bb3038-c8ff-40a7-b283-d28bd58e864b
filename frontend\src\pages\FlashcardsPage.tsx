import React, { useState, Change<PERSON><PERSON>, useEffect } from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal, FilePenLine, Trash2 } from "lucide-react";
import AppLayout from "@/components/layout/AppLayout";
import {
  FlashcardSet as SharedFlashcardDeck,
  Flashcard as SharedFlashcard,
} from "@shared/types/flashcards";
import { FlashcardDeck, Flashcard } from "@/types";
import { generateFlashcardsAPI } from "@/lib/api";
import {
  getAIProviderSettings,
  isAIProviderConfigured,
} from "@/lib/ai-provider";
import { extractTextFromFile } from "@/lib/file-parser";
// FlashcardSetList import removed
import { QuizGenerationPopup } from "../components/flashcards/QuizGenerationPopup";
import { useLocation } from "wouter";
import { useToast } from "@/hooks/use-toast";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import { Tables } from "@/types/supabase";
import Spinner from "@/components/ui/Spinner";
import { API_BASE_URL } from "@/lib/config";

interface QuizGenerationOptions {
  numberOfQuestions: number;
  questionTypes: string[];
  customPrompt: string;
}

const FlashcardsPage: React.FC = () => {
  const { user } = useAuth();

  // File upload states
  const [uploadingFile, setUploadingFile] = useState<boolean>(false);

  // Document selection states
  const [availableDocuments, setAvailableDocuments] = useState<
    Tables<"study_documents">[]
  >([]);
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);
  const [customPrompt, setCustomPrompt] = useState<string>("");

  // Common states
  const [deckTitle, setDeckTitle] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generatedDeck, setGeneratedDeck] = useState<FlashcardDeck | null>(
    null
  );
  const [userDecks, setUserDecks] = useState<FlashcardDeck[]>([]);
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [generatingQuizForDeck, setGeneratingQuizForDeck] = useState<
    string | null
  >(null);
  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);

  // Check AI configuration on mount
  useEffect(() => {
    const checkAIConfig = async () => {
      const configured = await isAIProviderConfigured();
      setIsAIConfigured(configured);
    };
    checkAIConfig();
  }, []);

  // Load existing decks on mount
  useEffect(() => {
    const fetchDecks = async () => {
      if (!user) return;

      try {
        const token = localStorage.getItem("auth_token");

        if (!token) {
          console.warn("No auth token found when loading decks");
          return;
        }

        const flashcardSetsResponse = await fetch(
          `${API_BASE_URL}/flashcard-sets`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (!flashcardSetsResponse.ok) {
          console.error(
            "Failed to fetch flashcard sets:",
            flashcardSetsResponse.statusText
          );
          return;
        }

        const flashcardSets = await flashcardSetsResponse.json();

        // Convert Supabase flashcard sets to client format
        const decks: FlashcardDeck[] = flashcardSets.map((set: any) => ({
          id: set.id,
          name: set.name,
          description: set.description || "",
          documentId: set.study_document_id || "",
          createdAt: set.created_at
            ? new Date(set.created_at).getTime()
            : Date.now(),
          totalCards: set.card_count || 0, // Use the card count from backend
          dueTodayCount: set.card_count || 0, // All cards are available for review
          masteredCount: 0,
        }));

        setUserDecks(decks);
      } catch (error) {
        console.error("Error fetching flashcard sets:", error);
      }
    };

    fetchDecks();
  }, [user]);

  // Load documents on mount
  useEffect(() => {
    const fetchDocuments = async () => {
      if (user) {
        setLoadingDocuments(true);
        try {
          const token = localStorage.getItem("auth_token");
          if (!token) {
            throw new Error("No authentication token found");
          }

          const documentsResponse = await fetch(`${API_BASE_URL}/documents`, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          });

          if (!documentsResponse.ok) {
            throw new Error(
              `HTTP ${documentsResponse.status}: ${documentsResponse.statusText}`
            );
          }

          const data = await documentsResponse.json();
          // Filter for extracted documents only
          const extractedDocs = data.filter(
            (doc: any) => doc.status === "extracted"
          );
          setAvailableDocuments(extractedDocs || []);
        } catch (err: any) {
          console.error(
            "Error fetching documents for flashcard generation:",
            err
          );
          toast({
            title: "Error",
            description: "Could not load documents for AI generation.",
            variant: "destructive",
          });
        } finally {
          setLoadingDocuments(false);
        }
      }
    };
    fetchDocuments();
  }, [user, toast]);

  // Document selection handler
  const handleDocumentSelection = (documentId: string) => {
    setSelectedDocumentIds((prev) =>
      prev.includes(documentId)
        ? prev.filter((id) => id !== documentId)
        : [...prev, documentId]
    );
  };

  const handleFileUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    setUploadingFile(true);
    setError(null);

    try {
      // Extract text from file
      const doc = await extractTextFromFile(file);
      if (doc.content.trim().length === 0) {
        throw new Error(
          "Extracted text is empty. The document might be image-based or corrupted."
        );
      }

      // Upload to server
      const token = localStorage.getItem("auth_token");
      if (!token) {
        throw new Error(
          "No authentication session found. Please log in again."
        );
      }

      const uploadResponse = await fetch(`${API_BASE_URL}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          fileName: file.name,
          content: doc.content,
          contentType: file.type,
          sizeBytes: file.size,
        }),
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            `HTTP ${uploadResponse.status}: ${uploadResponse.statusText}`
        );
      }

      const uploadedDoc = await uploadResponse.json();

      // Add to available documents and auto-select it
      setAvailableDocuments((prev) => [uploadedDoc, ...prev]);
      setSelectedDocumentIds((prev) => [...prev, uploadedDoc.id]);

      // Set deck title from filename if not already set
      if (!deckTitle.trim()) {
        setDeckTitle(file.name.split(".").slice(0, -1).join("."));
      }

      toast({
        title: "Document Uploaded",
        description: `"${file.name}" has been uploaded and selected for flashcard generation.`,
      });

      // Clear file input
      event.target.value = "";
    } catch (e) {
      console.error("Error uploading file:", e);
      setError(
        e instanceof Error ? e.message : "Failed to upload and process file."
      );
    } finally {
      setUploadingFile(false);
    }
  };

  const mapSharedDeckToClient = (
    sharedDeck: any
  ): { deck: FlashcardDeck; cards: Flashcard[] } => {
    console.log("🔍 Mapping shared deck to client:", sharedDeck);

    if (!sharedDeck) {
      throw new Error("Received undefined or null deck from server");
    }

    // Handle the new response format with 'set' and 'flashcards' properties
    let setData = sharedDeck.set || sharedDeck;
    let flashcardsData = sharedDeck.flashcards || [];

    // Handle legacy format with 'deck' property
    if ("deck" in sharedDeck && sharedDeck.deck) {
      setData = sharedDeck.deck;
      flashcardsData = setData.flashcards || [];
    }

    const deck: FlashcardDeck = {
      id: setData.id || crypto.randomUUID(),
      name: setData.name || setData.title || "Untitled Deck",
      description:
        setData.description ||
        `Generated from document ${
          setData.study_document_id || setData.documentId || "unknown"
        }`,
      documentId: setData.study_document_id || setData.documentId || "",
      createdAt: setData.created_at
        ? new Date(setData.created_at).getTime()
        : setData.createdAt || Date.now(),
      totalCards: 0, // Will update after processing flashcards
      dueTodayCount: 0,
      masteredCount: 0,
    };

    const cards: Flashcard[] = [];

    // Process flashcards if available
    if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {
      cards.push(
        ...flashcardsData.map((fc: any) => ({
          id: fc.id || crypto.randomUUID(),
          question: fc.front_text || fc.question || "No question",
          answer: fc.back_text || fc.answer || "No answer",
          deckId: fc.set_id || fc.deckId || deck.id,
          createdAt: fc.created_at
            ? new Date(fc.created_at).getTime()
            : Date.now(),
        }))
      );

      // Update totalCards with actual count
      deck.totalCards = cards.length;
      deck.dueTodayCount = cards.length;
    }

    console.log("✅ Mapped deck:", { deck, cardsCount: cards.length });
    return { deck, cards };
  };

  // New AI-powered flashcard generation from multiple documents
  const handleAiFlashcardGeneration = async () => {
    if (!isAIConfigured) {
      setError(
        "AI Provider not configured. Please configure your AI settings first."
      );
      return;
    }

    if (selectedDocumentIds.length === 0) {
      setError("Please select at least one document.");
      return;
    }

    if (!deckTitle.trim()) {
      setError("Please provide a deck title.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const aiSettings = getAIProviderSettings();
      const token = localStorage.getItem("auth_token");

      if (!token) {
        throw new Error(
          "No authentication session found. Please log in again."
        );
      }

      // Fetch document contents using the API
      const documentContents: string[] = [];
      for (const docId of selectedDocumentIds) {
        try {
          const documentContentResponse = await fetch(
            `${API_BASE_URL}/documents/${docId}/content`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (!documentContentResponse.ok) {
            throw new Error(
              `Failed to fetch document ${docId}: HTTP ${documentContentResponse.status}`
            );
          }

          const textContent = await documentContentResponse.text();
          if (textContent.trim()) {
            documentContents.push(textContent);
          }
        } catch (err) {
          console.error(`Error fetching document ${docId}:`, err);
          throw new Error(
            `Failed to fetch document ${docId}: ${
              err instanceof Error ? err.message : "Unknown error"
            }`
          );
        }
      }

      if (documentContents.length === 0) {
        throw new Error("No valid document content found.");
      }

      // Combine all document contents
      const combinedContent = documentContents.join(
        "\n\n--- Document Separator ---\n\n"
      );

      // Enhanced API payload with custom prompt support
      const enhancedPayload = {
        textContent: combinedContent,
        documentId: selectedDocumentIds.join(","), // Multiple document IDs
        deckTitle: deckTitle,
        customPrompt: customPrompt || undefined,
        // aiSettings removed - credentials are retrieved from secure backend storage
      };

      console.log("📤 Sending flashcard generation request:", enhancedPayload);
      const sharedDeck = await generateFlashcardsAPI(enhancedPayload);
      console.log("📥 Received flashcard generation response:", sharedDeck);

      if (!sharedDeck) {
        throw new Error("No response received from flashcard generation API");
      }

      const { deck, cards } = mapSharedDeckToClient(sharedDeck);

      // Save flashcard set via Express.js backend
      const authToken = localStorage.getItem("auth_token");

      if (!authToken) {
        throw new Error(
          "No authentication session found. Please log in again."
        );
      }

      const createSetPayload = {
        name: deck.name,
        description:
          deck.description ||
          `Generated from ${selectedDocumentIds.length} document(s)`,
        study_document_id: selectedDocumentIds[0] || null, // Use first document ID
        flashcards: cards.map((card) => ({
          front_text: card.question,
          back_text: card.answer,
        })),
      };

      const response = await fetch(`${API_BASE_URL}/flashcard-sets`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(createSetPayload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      const savedFlashcardSet = await response.json();

      // Update deck with the correct backend ID and card count
      const updatedDeck = {
        ...deck,
        id: savedFlashcardSet.id,
        name: savedFlashcardSet.name,
        totalCards: cards.length,
        dueTodayCount: cards.length,
      };

      setGeneratedDeck(updatedDeck);
      setUserDecks((prev) => [updatedDeck, ...prev]);

      // Clear selections
      setSelectedDocumentIds([]);
      setCustomPrompt("");
      setDeckTitle("");

      toast({
        title: "Success!",
        description: `Generated "${deck.name}" with ${cards.length} flashcards from ${selectedDocumentIds.length} document(s).`,
      });
    } catch (e) {
      console.error("Error generating flashcards from documents:", e);
      setError(
        e instanceof Error
          ? e.message
          : "Failed to generate flashcards from documents."
      );
    }
    setIsLoading(false);
  };

  const handleManageDeck = (deckId: string) => {
    navigate(`/flashcards/edit/${deckId}`);
  };

  const handleDeleteDeck = async (deckId: string, deckName: string) => {
    if (
      !window.confirm(
        `Are you sure you want to delete the deck "${deckName}"? All flashcards in this deck will also be deleted.`
      )
    )
      return;

    try {
      const token = localStorage.getItem("auth_token");

      if (!token) {
        throw new Error(
          "No authentication session found. Please log in again."
        );
      }

      const response = await fetch(`${API_BASE_URL}/flashcard-sets/${deckId}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      // Remove from local state
      setUserDecks((prev) => prev.filter((deck) => deck.id !== deckId));

      toast({
        title: "Success",
        description: `Deck "${deckName}" has been deleted.`,
      });
    } catch (error) {
      console.error("Error deleting deck:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete the deck. Please try again.",
        variant: "destructive",
      });
    }
  };
  const handleGenerateQuizFromDeck = async (
    deck: FlashcardDeck,
    options: QuizGenerationOptions
  ) => {
    if (!isAIConfigured) {
      toast({
        title: "AI Provider Not Configured",
        description:
          "Please configure your AI provider settings to generate quizzes.",
        variant: "destructive",
      });
      return;
    }

    if (generatingQuizForDeck) {
      return; // Prevent multiple simultaneous generations
    }

    setGeneratingQuizForDeck(deck.id);

    try {
      // Fetch flashcards from backend API instead of local storage
      const token = localStorage.getItem("auth_token");

      if (!token) {
        throw new Error(
          "No authentication session found. Please log in again."
        );
      }

      console.log("🔍 Fetching flashcards for deck:", {
        deckId: deck.id,
        deckName: deck.name,
        totalCards: deck.totalCards,
      });

      const flashcardResponse = await fetch(
        `${API_BASE_URL}/flashcard-sets/${deck.id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!flashcardResponse.ok) {
        const errorData = await flashcardResponse.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            `Failed to fetch flashcards: ${flashcardResponse.statusText}`
        );
      }

      const flashcardSetData = await flashcardResponse.json();
      const flashcards = flashcardSetData.flashcards || [];

      console.log("📥 Fetched flashcards:", {
        deckId: deck.id,
        flashcardCount: flashcards.length,
        flashcardSetData: flashcardSetData,
      });

      if (flashcards.length === 0) {
        toast({
          title: "No Flashcards Found",
          description: "This deck has no flashcards to generate a quiz from.",
          variant: "destructive",
        });
        return;
      }

      // Map flashcards from backend format to expected format
      const textContent = flashcards
        .map(
          (card: any) =>
            `Question: ${card.front_text || card.question}\nAnswer: ${
              card.back_text || card.answer
            }`
        )
        .join("\n\n");

      const aiSettings = getAIProviderSettings();
      const quizTitle = `Quiz: ${deck.name}`;
      const numberOfQuestions = Math.min(
        flashcards.length,
        options.numberOfQuestions
      );

      console.log("🎯 Quiz Generation Debug Info:", {
        deckId: deck.id,
        deckName: deck.name,
        flashcardCount: flashcards.length,
        textContentLength: textContent.length,
        quizTitle,
        numberOfQuestions,
        aiProvider: aiSettings.provider,
        aiModel: aiSettings.generationModel,
        hasApiKey: !!aiSettings.apiKey,
        sampleFlashcard: flashcards[0], // Log first flashcard for debugging
      });
      const requestBody = {
        textContent,
        quizName: quizTitle,
        generationOptions: {
          numberOfQuestions,
          questionTypes: options.questionTypes,
          customPrompt: options.customPrompt,
        },
        // AI config is now handled by the backend using stored credentials
      };

      console.log("📤 Sending quiz generation request:", {
        url: `${API_BASE_URL}/quizzes/generate`,
        method: "POST",
        hasAuth: !!token,
        bodyKeys: Object.keys(requestBody),
      });

      const generateQuizResponse = await fetch(
        `${API_BASE_URL}/quizzes/generate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      console.log("📥 Quiz generation response:", {
        status: generateQuizResponse.status,
        statusText: generateQuizResponse.statusText,
        ok: generateQuizResponse.ok,
      });

      if (!generateQuizResponse.ok) {
        const errorData = await generateQuizResponse.json().catch(() => ({}));
        console.error("❌ Quiz generation error details:", errorData);
        throw new Error(
          errorData.error ||
            errorData.message ||
            `HTTP ${generateQuizResponse.status}: ${generateQuizResponse.statusText}`
        );
      }

      const quizData = await generateQuizResponse.json();
      console.log("✅ Quiz generated successfully:", {
        quizId: quizData.quizId || quizData.id,
        hasQuiz: !!quizData.quiz,
      });

      const finalQuizId = quizData.quizId || quizData.id;
      if (!finalQuizId) {
        throw new Error("No quiz ID returned from server");
      }
      toast({
        title: "Quiz Generated Successfully",
        description: `Created "${quizTitle}" with ${numberOfQuestions} questions!`,
      });

      // Navigate to quizzes page and show generating notification
      navigate("/quizzes");

      // Show a temporary notification that the quiz is being added to the list
      setTimeout(() => {
        toast({
          title: "Quiz Ready",
          description: `"${quizTitle}" has been added to your quizzes!`,
        });
      }, 1000);
    } catch (error: any) {
      console.error("❌ Error generating quiz:", error);
      console.error("Error stack:", error.stack);
      toast({
        title: "Quiz Generation Failed",
        description:
          error.message || "Failed to generate quiz from flashcards.",
        variant: "destructive",
      });
    } finally {
      setGeneratingQuizForDeck(null);
    }
  };

  return (
    <AppLayout title="Flashcards">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-slate-800 dark:text-slate-100">
            Flashcard Sets
          </h1>
        </div>

        {/* Create New Deck Card */}
        <Card className="mb-8 bg-purple-900 bg-opacity-20 border-purple-700">
          <CardHeader>
            <CardTitle className="text-slate-100">Create New Deck</CardTitle>
            <CardDescription className="text-purple-300">
              Select documents and customize AI generation settings, or upload a
              new document.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Deck Title */}
              <div>
                <Label htmlFor="deck-title" className="text-slate-300">
                  Deck Title*
                </Label>
                <Input
                  id="deck-title"
                  type="text"
                  value={deckTitle}
                  onChange={(e) => setDeckTitle(e.target.value)}
                  placeholder="e.g., Chapter 1 Flashcards"
                  disabled={isLoading || uploadingFile}
                  className="mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>

              {/* File Upload Section */}
              <div>
                <Label htmlFor="file-upload" className="text-slate-300">
                  Upload New Document (Optional)
                </Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".pdf,.docx,.txt,.md"
                  onChange={handleFileUpload}
                  disabled={isLoading || uploadingFile}
                  className="mt-1 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 file:border-0 file:bg-transparent file:text-purple-400"
                />
                <p className="text-xs text-slate-400 mt-1">
                  {uploadingFile
                    ? "Uploading and processing..."
                    : "Upload a PDF, DOCX, TXT, or MD file to add to your documents"}
                </p>
              </div>

              {/* Document Selection */}
              <div>
                <Label className="text-slate-300 mb-2 block">
                  Select Documents for AI Generation*
                </Label>
                {loadingDocuments ? (
                  <div className="flex justify-center items-center py-4">
                    <Spinner size="sm" />
                    <span className="ml-2 text-purple-300">
                      Loading documents...
                    </span>
                  </div>
                ) : availableDocuments.length === 0 ? (
                  <p className="text-sm text-purple-300 p-3 bg-slate-700 rounded-md">
                    No extracted documents available. Please upload and process
                    documents first.
                  </p>
                ) : (
                  <div className="max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600">
                    {availableDocuments.map((doc) => (
                      <div
                        key={doc.id}
                        className="flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors"
                      >
                        <Checkbox
                          id={`ai-doc-${doc.id}`}
                          checked={selectedDocumentIds.includes(doc.id)}
                          onCheckedChange={() =>
                            handleDocumentSelection(doc.id)
                          }
                          disabled={isLoading}
                        />
                        <Label
                          htmlFor={`ai-doc-${doc.id}`}
                          className="font-normal text-purple-300 cursor-pointer flex-1 truncate"
                          title={doc.file_name}
                        >
                          {doc.file_name}
                        </Label>
                      </div>
                    ))}
                  </div>
                )}
                {selectedDocumentIds.length > 0 && (
                  <p className="text-xs text-purple-400 mt-1">
                    {selectedDocumentIds.length} document(s) selected.
                  </p>
                )}
              </div>

              {/* Custom Prompt */}
              <div>
                <Label htmlFor="custom-prompt" className="text-slate-300">
                  Custom Prompt (Optional)
                </Label>
                <Textarea
                  id="custom-prompt"
                  value={customPrompt}
                  onChange={(e) => setCustomPrompt(e.target.value)}
                  placeholder="e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'"
                  rows={3}
                  className="mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500"
                  disabled={isLoading}
                />
                <p className="text-xs text-purple-400 mt-1">
                  Add specific instructions for the AI on what kind of
                  flashcards you want.
                </p>
              </div>

              {error && (
                <Alert variant="destructive">
                  <Terminal className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                onClick={handleAiFlashcardGeneration}
                disabled={
                  isLoading ||
                  uploadingFile ||
                  selectedDocumentIds.length === 0 ||
                  !deckTitle.trim() ||
                  !isAIConfigured
                }
                className="w-full"
              >
                {isLoading
                  ? "Generating Flashcards..."
                  : "Generate Flashcards with AI"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {generatedDeck && (
          <Card className="mb-8 bg-green-900 bg-opacity-20 border-green-700">
            <CardHeader>
              <CardTitle className="text-green-300">Deck Generated!</CardTitle>
              <CardDescription className="text-green-400">
                {generatedDeck.totalCards} cards ready.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href={`/flashcards/${generatedDeck.id}`}>
                <Button>Review Deck</Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Existing Decks */}
        <h2 className="text-2xl font-semibold mb-4 text-slate-100">My Decks</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {userDecks.map((deck) => (
            <Card
              key={deck.id}
              className="bg-purple-800 bg-opacity-20 border-purple-700"
            >
              <CardHeader>
                <CardTitle className="text-slate-200">{deck.name}</CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col space-y-4">
                <p className="text-slate-400">{deck.totalCards} Cards</p>
                <div className="grid grid-cols-2 gap-3">
                  {" "}
                  {/* 2x2 grid with consistent spacing */}
                  {/* Row 1: Review & Generate Quiz */}
                  <Link href={`/flashcards/${deck.id}`} className="w-full">
                    <Button className="w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer">
                      Review
                    </Button>
                  </Link>
                  <QuizGenerationPopup
                    trigger={
                      <Button
                        disabled={generatingQuizForDeck === deck.id}
                        className="w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {generatingQuizForDeck === deck.id
                          ? "Generating..."
                          : "Generate Quiz"}
                      </Button>
                    }
                    onGenerate={(options) =>
                      handleGenerateQuizFromDeck(deck, options)
                    }
                    isGenerating={generatingQuizForDeck === deck.id}
                    disabled={generatingQuizForDeck === deck.id}
                    maxQuestions={deck.totalCards || 20}
                  />
                  {/* Row 2: Manage & Delete */}
                  <Button
                    onClick={() => handleManageDeck(deck.id)}
                    className="w-full h-10 bg-purple-600 hover:bg-purple-700 text-white cursor-pointer"
                  >
                    <FilePenLine size={16} className="mr-1" /> Manage
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={() => handleDeleteDeck(deck.id, deck.name)}
                    className="w-full h-10 cursor-pointer"
                  >
                    <Trash2 size={16} className="mr-1" /> Delete
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
          {userDecks.length === 0 && (
            <p className="text-slate-400">No decks yet. Generate one above!</p>
          )}
        </div>
      </div>
    </AppLayout>
  );
};

export default FlashcardsPage;
