#!/usr/bin/env bash
set -euo pipefail

echo "🚂 Deploying ChewyAI to Railway (Full-Stack)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[RAILWAY]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check if Railway CLI is installed
if ! command -v railway >/dev/null 2>&1; then
    print_error "Railway CLI is not installed. Installing..."
    npm install -g @railway/cli
fi

print_success "Railway CLI is available"

# Login to Railway
print_status "Checking Railway authentication..."
if ! railway whoami >/dev/null 2>&1; then
    print_warning "Not logged in to Railway"
    print_status "Logging in to Railway..."
    railway login
fi

print_success "Authenticated with Railway"

# Check if project is already linked
if [ ! -f ".railway/project.json" ]; then
    print_status "Creating new Railway project..."
    railway init
else
    print_status "Using existing Railway project"
fi

# Create railway.json configuration
print_status "Creating Railway configuration..."
cat > railway.json << 'EOF'
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "numReplicas": 1,
    "sleepApplication": false,
    "restartPolicyType": "ON_FAILURE"
  }
}
EOF

# Create Dockerfile for Railway
print_status "Creating Dockerfile..."
cat > Dockerfile << 'EOF'
# Multi-stage build for ChewyAI
FROM node:18-alpine AS base
WORKDIR /app

# Install dependencies for both frontend and backend
FROM base AS deps
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/
RUN npm ci
RUN cd frontend && npm ci
RUN cd backend && npm ci

# Build frontend
FROM base AS frontend-builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules
COPY frontend ./frontend
COPY shared ./shared
RUN cd frontend && npm run build

# Build backend
FROM base AS backend-builder
COPY --from=deps /app/backend/node_modules ./backend/node_modules
COPY backend ./backend
COPY shared ./shared
RUN cd backend && npm run build

# Production image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy built applications
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist
COPY --from=frontend-builder /app/frontend/server.js ./frontend/
COPY --from=backend-builder /app/backend/dist ./backend/dist
COPY --from=deps /app/backend/node_modules ./backend/node_modules
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules

# Copy shared files
COPY shared ./shared
COPY package*.json ./

# Install production dependencies
RUN npm ci --only=production

# Create startup script
RUN echo '#!/bin/sh' > start.sh && \
    echo 'cd /app/backend && node dist/index.cjs &' >> start.sh && \
    echo 'cd /app/frontend && node server.js &' >> start.sh && \
    echo 'wait' >> start.sh && \
    chmod +x start.sh

EXPOSE 3000 5000

CMD ["./start.sh"]
EOF

# Set environment variables
print_status "Setting Railway environment variables..."

# Get environment variables from user
echo ""
print_status "Environment Variable Configuration"
echo "Please provide the following environment variables:"

read -p "SUPABASE_URL: " SUPABASE_URL
read -p "SUPABASE_SERVICE_ROLE_KEY: " SUPABASE_SERVICE_ROLE_KEY
read -p "SUPABASE_ANON_KEY: " SUPABASE_ANON_KEY

# Set environment variables
railway variables set NODE_ENV=production
railway variables set PORT=3000
railway variables set BACKEND_PORT=5000
railway variables set SUPABASE_URL="$SUPABASE_URL"
railway variables set SUPABASE_SERVICE_ROLE_KEY="$SUPABASE_SERVICE_ROLE_KEY"
railway variables set VITE_SUPABASE_URL="$SUPABASE_URL"
railway variables set VITE_SUPABASE_ANON_KEY="$SUPABASE_ANON_KEY"
railway variables set VITE_API_BASE_URL="/api"

print_success "Environment variables set"

# Deploy to Railway
print_status "Deploying to Railway..."
railway up

if [ $? -eq 0 ]; then
    print_success "🎉 Deployment completed!"
    
    # Get the deployment URL
    RAILWAY_URL=$(railway domain 2>/dev/null || echo "")
    
    if [ -n "$RAILWAY_URL" ]; then
        print_success "🌐 Your app is live at: $RAILWAY_URL"
        echo ""
        print_status "Available endpoints:"
        echo "  - Frontend: $RAILWAY_URL"
        echo "  - Backend API: $RAILWAY_URL/api"
        echo "  - Health check: $RAILWAY_URL/api/health"
    else
        print_status "Get your deployment URL with: railway domain"
    fi
    
    echo ""
    print_status "Next steps:"
    echo "1. Test your application at the Railway URL"
    echo "2. Set up a custom domain if needed: railway domain add"
    echo "3. Monitor logs: railway logs"
    
else
    print_error "Deployment failed"
    print_status "Check logs with: railway logs"
    exit 1
fi
