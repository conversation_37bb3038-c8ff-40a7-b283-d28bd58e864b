# Netlify configuration for ChewyAI
[build]
  # Build command for the frontend
  command = "npm run build:frontend"
  
  # Directory to publish (frontend build output)
  publish = "frontend/dist"
  
  # Base directory for build
  base = "."

[build.environment]
  # Node.js version
  NODE_VERSION = "18"
  
  # Build environment
  NODE_ENV = "production"

# Redirect rules for SPA (Single Page Application)
# Note: API redirects removed - frontend will use direct backend URLs
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Cache control for static assets
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "/index.html"
  [headers.values]
    # Don't cache the main HTML file
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/api/*"
  [headers.values]
    # CORS headers for API routes
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization"

# Edge functions configuration (if using Netlify Edge Functions)
# Example edge function for API proxy
# [[edge_functions]]
#   function = "api-proxy"
#   path = "/api/*"

# Plugin configuration
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs.thresholds]
    performance = 0.8
    accessibility = 0.9
    best-practices = 0.8
    seo = 0.8

# Development settings
[dev]
  # Local development command
  command = "npm run dev"
  
  # Port for local development
  port = 3000
  
  # Auto-open browser
  autoLaunch = true
  
  # Framework detection
  framework = "#auto"

# Context-specific settings
[context.production]
  [context.production.environment]
    NODE_ENV = "production"
    REACT_APP_ENV = "production"
    # Set your production backend URL here
    VITE_API_BASE_URL = "https://your-backend-domain.com/api"

[context.deploy-preview]
  [context.deploy-preview.environment]
    NODE_ENV = "production"
    REACT_APP_ENV = "preview"
    # Use staging backend for previews
    VITE_API_BASE_URL = "https://your-staging-backend.com/api"

[context.branch-deploy]
  [context.branch-deploy.environment]
    NODE_ENV = "production"
    REACT_APP_ENV = "branch"
    # Use staging backend for branch deploys
    VITE_API_BASE_URL = "https://your-staging-backend.com/api"

# Functions configuration (if using Netlify Functions)
[functions]
  # Directory for serverless functions
  directory = "netlify/functions"
  
  # Node.js runtime
  node_bundler = "esbuild"
