functionsDirectory = "C:\\Users\\<USER>\\Documents\\Github\\chewyai\\netlify\\functions"
functionsDirectoryOrigin = "config"
headersOrigin = "config"
redirectsOrigin = "config"

[dev]
command = "npm run dev"
port = 3000.0
autoLaunch = true
framework = "#auto"

[functions]

[functions."*"]
node_bundler = "esbuild"

[[plugins]]
origin = "config"
package = "@netlify/plugin-lighthouse"

[plugins.inputs]

[plugins.inputs.thresholds]
performance = 0.8
accessibility = 0.9
best-practices = 0.8
seo = 0.8

[build]
publish = "C:\\Users\\<USER>\\Documents\\Github\\chewyai\\frontend\\dist"
publishOrigin = "config"
commandOrigin = "config"
command = "npm run build:frontend"
base = "C:\\Users\\<USER>\\Documents\\Github\\chewyai"
functions = "C:\\Users\\<USER>\\Documents\\Github\\chewyai\\netlify\\functions"

[build.environment]
NODE_VERSION = "18"
NODE_ENV = "production"
REACT_APP_ENV = "production"

[build.processing]

[build.processing.css]

[build.processing.html]

[build.processing.images]

[build.processing.js]

[build.services]

[[headers]]
for = "/*"

[headers.values]
X-Frame-Options = "DENY"
X-XSS-Protection = "1; mode=block"
X-Content-Type-Options = "nosniff"
Referrer-Policy = "strict-origin-when-cross-origin"
Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
for = "/index.html"

[headers.values]
Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
for = "/api/*"

[headers.values]
Access-Control-Allow-Origin = "*"
Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
Access-Control-Allow-Headers = "Content-Type, Authorization"

[[redirects]]
from = "/api/*"
to = "/.netlify/functions/:splat"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]

[[redirects]]
from = "/*"
to = "/index.html"
status = 200.0
force = false

[redirects.query]

[redirects.conditions]

[redirects.headers]