#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Starting ChewyAI in production mode..."

# Check if build files exist
if [ ! -f "dist/index.js" ]; then
  echo "❌ Backend build not found. Run 'npm run build' first."
  exit 1
fi

if [ ! -f "dist/frontend-server.js" ]; then
  echo "❌ Frontend server build not found. Run 'npm run build' first."
  exit 1
fi

if [ ! -d "dist/public" ]; then
  echo "❌ Frontend build not found. Run 'npm run build' first."
  exit 1
fi

echo "✅ All build files found"

# Set production environment
export NODE_ENV=production

# Start both servers with concurrently
echo "🔧 Starting backend server on port 5000..."
echo "🌐 Starting frontend server on port 3000..."

concurrently \
  --names "BACKEND,FRONTEND" \
  --prefix-colors "green,cyan" \
  --kill-others \
  "node dist/index.js" \
  "node dist/frontend-server.js"
