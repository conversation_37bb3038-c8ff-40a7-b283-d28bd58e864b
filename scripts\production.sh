#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Starting ChewyAI in production mode..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[PROD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check if build files exist in correct locations
print_status "Checking build files..."

if [ ! -f "backend/dist/index.cjs" ]; then
  print_error "Backend build not found at backend/dist/index.cjs"
  print_status "Run 'npm run build:backend' or 'npm run build' first."
  exit 1
fi

if [ ! -f "frontend/dist/index.html" ]; then
  print_error "Frontend build not found at frontend/dist/index.html"
  print_status "Run 'npm run build:frontend' or 'npm run build' first."
  exit 1
fi

if [ ! -f "frontend/server.js" ]; then
  print_error "Frontend server not found at frontend/server.js"
  exit 1
fi

print_success "All build files found"

# Set production environment
export NODE_ENV=production
export BACKEND_URL=${BACKEND_URL:-"http://localhost:5000"}

print_status "Environment configuration:"
echo "  - NODE_ENV: $NODE_ENV"
echo "  - Backend URL: $BACKEND_URL"

# Start both servers with concurrently
print_status "Starting production servers..."
print_status "Backend (Express): http://localhost:5000"
print_status "Frontend (Express + Static): http://localhost:3000"
print_warning "Press Ctrl+C to stop all servers"

concurrently \
  --names "BACKEND,FRONTEND" \
  --prefix-colors "green,cyan" \
  --kill-others \
  "cd backend && node dist/index.cjs" \
  "cd frontend && node server.js"
