import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import Spinner from "@/components/ui/Spinner";

export interface GenerationOptions {
  numberOfQuestions: number;
  questionTypes: string[];
}

interface AiQuizGenerationOptionsProps {
  generationOptions: GenerationOptions;
  setGenerationOptions: React.Dispatch<React.SetStateAction<GenerationOptions>>;
  isGenerating: boolean;
  onGenerate: () => void;
  documentIds?: string[]; // To confirm if documents are selected
}

const QUESTION_TYPE_OPTIONS = [
  { id: "multiple_choice", label: "Multiple Choice" },
  { id: "select_all_that_apply", label: "Select All That Apply" },
  { id: "true_false", label: "True/False" },
  { id: "short_answer", label: "Short Answer" },
];

export const AiQuizGenerationOptions: React.FC<
  AiQuizGenerationOptionsProps
> = ({
  generationOptions,
  setGenerationOptions,
  isGenerating,
  onGenerate,
  documentIds = [],
}) => {
  const hasDocuments = documentIds && documentIds.length > 0;

  const handleQuestionTypeChange = (typeId: string) => {
    setGenerationOptions((prev) => ({
      ...prev,
      questionTypes: prev.questionTypes.includes(typeId)
        ? prev.questionTypes.filter((qt) => qt !== typeId)
        : [...prev.questionTypes, typeId],
    }));
  };

  if (!hasDocuments) {
    return (
      <p className="text-sm text-purple-300 mt-4 p-3 bg-slate-700 rounded-md">
        Please select a study document first to enable AI Quiz Generation. You
        can typically do this from the document list or viewer.
      </p>
    );
  }

  return (
    <div className="space-y-4 mt-6 pt-4 border-t border-slate-700">
      <h4 className="text-md font-semibold text-purple-400 mb-3">
        AI Generation Options
      </h4>
      <div>
        <Label htmlFor="numberOfQuestions" className="text-purple-300">
          Number of Questions
        </Label>
        <Input
          id="numberOfQuestions"
          type="text"
          value={generationOptions.numberOfQuestions}
          onChange={(e) =>
            setGenerationOptions((prev) => ({
              ...prev,
              numberOfQuestions: parseInt(e.target.value, 10) || 5,
            }))
          }
          className="mt-1 bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus-visible:ring-purple-500"
          disabled={isGenerating}
        />
      </div>
      <div>
        <Label className="text-purple-300">Question Types</Label>
        <div className="mt-2 space-y-2">
          {QUESTION_TYPE_OPTIONS.map((type) => (
            <div key={type.id} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${type.id}`}
                checked={generationOptions.questionTypes.includes(type.id)}
                onCheckedChange={() => handleQuestionTypeChange(type.id)}
                disabled={isGenerating}
              />
              <Label
                htmlFor={`type-${type.id}`}
                className="font-normal text-purple-300"
              >
                {type.label}
              </Label>
            </div>
          ))}
        </div>
        {generationOptions.questionTypes.length === 0 && (
          <p className="text-xs text-red-400 mt-1">
            Please select at least one question type.
          </p>
        )}
      </div>
      <Button
        onClick={onGenerate}
        disabled={
          isGenerating ||
          generationOptions.questionTypes.length === 0 ||
          !hasDocuments
        }
        className="w-full mt-2 bg-purple-600 hover:bg-purple-700 text-white"
      >
        {isGenerating && <Spinner size="sm" />}
        {isGenerating
          ? "Generating Questions..."
          : "Generate More Questions with AI"}
      </Button>
    </div>
  );
};
