{"name": "chewy-ai", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "dev": "concurrently --kill-others-on-fail --prefix \"[{name}]\" --names \"backend,frontend\" \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:setup": "bash dev.sh", "dev:verbose": "concurrently --kill-others-on-fail --prefix \"[{name}]\" --names \"backend,frontend\" --prefix-colors \"cyan,magenta\" \"npm run dev:backend\" \"npm run dev:frontend\"", "start:backend": "cd backend && npm run start", "start:frontend": "cd frontend && npm run start", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "start": "npm run start:backend", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"concurrently": "^8.2.2", "drizzle-orm": "^0.44.2", "drizzle-zod": "^0.8.2", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^20.11.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.2.2"}}