#!/usr/bin/env bash
set -euo pipefail

echo "⚙️ Configuring ChewyAI environment variables..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CONFIG]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Parse command line arguments
BACKEND_URL=""
ENVIRONMENT="production"

while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-url)
            BACKEND_URL="$2"
            shift 2
            ;;
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --backend-url <url>     Backend URL (e.g., https://api.example.com)"
            echo "  --environment <env>     Environment (production, staging, preview)"
            echo "  -h, --help              Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --backend-url https://api.chewyai.com"
            echo "  $0 --backend-url https://railway.app/your-backend --environment production"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if Netlify CLI is available
if ! command -v netlify >/dev/null 2>&1; then
    print_error "Netlify CLI is not installed. Please install it first:"
    echo "npm install -g netlify-cli"
    exit 1
fi

# Check if site is linked
if [ ! -f ".netlify/state.json" ]; then
    print_error "No Netlify site linked. Please run 'netlify link' first."
    exit 1
fi

SITE_ID=$(cat .netlify/state.json | grep -o '"siteId"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"siteId"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' || echo "")
if [ -n "$SITE_ID" ]; then
    print_status "Found linked site: $SITE_ID"
else
    print_error "Could not read site ID from .netlify/state.json"
    exit 1
fi

# Get backend URL if not provided
if [ -z "$BACKEND_URL" ]; then
    echo ""
    print_status "Backend URL Configuration"
    echo "Please provide your backend URL. Common options:"
    echo "1. Railway: https://your-app.railway.app"
    echo "2. Render: https://your-app.onrender.com"
    echo "3. Heroku: https://your-app.herokuapp.com"
    echo "4. Custom domain: https://api.yourdomain.com"
    echo ""
    read -p "Enter your backend URL (without /api): " BACKEND_URL
    
    if [ -z "$BACKEND_URL" ]; then
        print_error "Backend URL is required"
        exit 1
    fi
fi

# Validate URL format
if [[ ! "$BACKEND_URL" =~ ^https?:// ]]; then
    print_error "Backend URL must start with http:// or https://"
    exit 1
fi

# Remove trailing slash if present
BACKEND_URL=${BACKEND_URL%/}

# Construct API URL
API_URL="${BACKEND_URL}/api"

print_status "Configuration Summary:"
echo "  - Environment: $ENVIRONMENT"
echo "  - Backend URL: $BACKEND_URL"
echo "  - API URL: $API_URL"
echo "  - Site ID: $SITE_ID"

# Confirm configuration
echo ""
read -p "Continue with this configuration? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Configuration cancelled"
    exit 0
fi

# Set environment variables in Netlify
print_status "Setting Netlify environment variables..."

# Determine context flag
CONTEXT_FLAG=""
if [ "$ENVIRONMENT" = "production" ]; then
    CONTEXT_FLAG="--context production"
elif [ "$ENVIRONMENT" = "staging" ] || [ "$ENVIRONMENT" = "preview" ]; then
    CONTEXT_FLAG="--context deploy-preview"
fi

# Set the API base URL
print_status "Setting VITE_API_BASE_URL=$API_URL"
netlify env:set VITE_API_BASE_URL "$API_URL" $CONTEXT_FLAG
if [ $? -eq 0 ]; then
    print_success "Set VITE_API_BASE_URL=$API_URL"
else
    print_error "Failed to set VITE_API_BASE_URL"
    exit 1
fi

# Set the backend URL (for server-side proxy if needed)
print_status "Setting VITE_BACKEND_URL=$BACKEND_URL"
netlify env:set VITE_BACKEND_URL "$BACKEND_URL" $CONTEXT_FLAG
if [ $? -eq 0 ]; then
    print_success "Set VITE_BACKEND_URL=$BACKEND_URL"
else
    print_error "Failed to set VITE_BACKEND_URL"
    exit 1
fi

# Set environment type
print_status "Setting REACT_APP_ENV=$ENVIRONMENT"
netlify env:set REACT_APP_ENV "$ENVIRONMENT" $CONTEXT_FLAG
if [ $? -eq 0 ]; then
    print_success "Set REACT_APP_ENV=$ENVIRONMENT"
else
    print_error "Failed to set REACT_APP_ENV"
    exit 1
fi

print_success "🎉 Environment variables configured successfully!"

echo ""
print_status "Next steps:"
echo "1. Deploy your frontend: bash scripts/deploy.sh --prod"
echo "2. Test the API connection in your deployed app"
echo "3. Check Netlify environment variables: netlify env:list --context production"

echo ""
print_warning "Important: Make sure your backend is deployed and accessible at:"
echo "  $BACKEND_URL"

echo ""
print_status "To verify your backend is working, test these endpoints:"
echo "  - Health check: curl $BACKEND_URL/health"
echo "  - API status: curl $API_URL/health"
