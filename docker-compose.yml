version: "3.8"
services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chewy-backend
    ports:
      - "5000:5000"
    environment:
      - PORT=5000
      - NODE_ENV=production
      - FRONTEND_URL=http://localhost
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chewy-frontend
    depends_on:
      - backend
    ports:
      - "80:3000"
    environment:
      - NODE_ENV=production
      - BACKEND_URL=http://backend:5000
