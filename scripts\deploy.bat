@echo off
setlocal enabledelayedexpansion

echo 🚀 Deploying ChewyAI to Netlify...

REM Colors for Windows (limited support)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

REM Parse command line arguments
set PRODUCTION_DEPLOY=false
set SITE_ID=
set BUILD_COMMAND=npm run build:frontend
set PUBLISH_DIR=frontend\dist
set SKIP_BUILD=false

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--prod" (
    set PRODUCTION_DEPLOY=true
    shift
    goto :parse_args
)
if "%~1"=="--production" (
    set PRODUCTION_DEPLOY=true
    shift
    goto :parse_args
)
if "%~1"=="--site-id" (
    set SITE_ID=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--build-command" (
    set BUILD_COMMAND=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--publish-dir" (
    set PUBLISH_DIR=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--skip-build" (
    set SKIP_BUILD=true
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    echo Usage: %0 [OPTIONS]
    echo.
    echo Options:
    echo   --prod, --production    Deploy to production (default: preview^)
    echo   --site-id ^<id^>          Specify Netlify site ID
    echo   --build-command ^<cmd^>   Override build command
    echo   --publish-dir ^<dir^>     Override publish directory
    echo   --skip-build           Skip build step
    echo   --help                 Show this help message
    echo.
    echo Examples:
    echo   %0                     # Deploy preview
    echo   %0 --prod              # Deploy to production
    echo   %0 --site-id abc123    # Deploy to specific site
    exit /b 0
)
echo Unknown option: %~1
echo Use --help for usage information
exit /b 1

:args_done

REM Check if Netlify CLI is installed
echo %BLUE%[DEPLOY]%NC% Checking Netlify CLI...
netlify --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Netlify CLI is not installed or not in PATH
    echo %BLUE%[DEPLOY]%NC% Install it with: npm install -g netlify-cli
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Netlify CLI is available

REM Check if user is logged in to Netlify
echo %BLUE%[DEPLOY]%NC% Checking Netlify authentication...
netlify status >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Not logged in to Netlify
    echo %BLUE%[DEPLOY]%NC% Logging in to Netlify...
    netlify login
)

echo %GREEN%[SUCCESS]%NC% Authenticated with Netlify

REM Build the project if not skipping
if "%SKIP_BUILD%"=="false" (
    echo %BLUE%[DEPLOY]%NC% Building project for deployment...
    
    REM Check frontend dependencies
    if not exist "frontend\node_modules" (
        echo %YELLOW%[WARNING]%NC% Frontend dependencies not found. Installing...
        cd frontend
        npm install
        cd ..
    )
    
    REM Clean previous build
    echo %BLUE%[DEPLOY]%NC% Cleaning previous build...
    if exist "frontend\dist" rmdir /s /q "frontend\dist"
    
    REM Run build command
    echo %BLUE%[DEPLOY]%NC% Running build command: %BUILD_COMMAND%
    call %BUILD_COMMAND%
    if errorlevel 1 (
        echo %RED%[ERROR]%NC% Build failed
        exit /b 1
    )
    
    REM Verify build output
    if not exist "%PUBLISH_DIR%" (
        echo %RED%[ERROR]%NC% Build failed - publish directory '%PUBLISH_DIR%' not found
        exit /b 1
    )
    
    if not exist "%PUBLISH_DIR%\index.html" (
        echo %RED%[ERROR]%NC% Build failed - index.html not found in '%PUBLISH_DIR%'
        exit /b 1
    )
    
    echo %GREEN%[SUCCESS]%NC% Build completed successfully
) else (
    echo %YELLOW%[WARNING]%NC% Skipping build step
    
    REM Verify publish directory exists
    if not exist "%PUBLISH_DIR%" (
        echo %RED%[ERROR]%NC% Publish directory '%PUBLISH_DIR%' not found. Run build first or remove --skip-build flag.
        exit /b 1
    )
)

REM Deploy to Netlify
echo %BLUE%[DEPLOY]%NC% Deploying to Netlify...

set DEPLOY_ARGS=--dir=%PUBLISH_DIR%

if not "%SITE_ID%"=="" (
    set DEPLOY_ARGS=%DEPLOY_ARGS% --site=%SITE_ID%
)

if "%PRODUCTION_DEPLOY%"=="true" (
    echo %BLUE%[DEPLOY]%NC% Deploying to production...
    set DEPLOY_ARGS=%DEPLOY_ARGS% --prod
    
    REM Confirm production deployment
    echo.
    echo %YELLOW%[WARNING]%NC% You are about to deploy to PRODUCTION
    set /p CONFIRM="Are you sure you want to continue? (y/n): "
    if /i not "%CONFIRM%"=="y" (
        echo %BLUE%[DEPLOY]%NC% Deployment cancelled
        exit /b 0
    )
) else (
    echo %BLUE%[DEPLOY]%NC% Deploying preview...
)

REM Execute deployment
echo %BLUE%[DEPLOY]%NC% Executing: netlify deploy %DEPLOY_ARGS%
netlify deploy %DEPLOY_ARGS%

if errorlevel 1 (
    echo %RED%[ERROR]%NC% Deployment failed
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% 🎉 Deployment completed!
echo.
echo %BLUE%[DEPLOY]%NC% Deployment Summary:
echo   - Publish Directory: %PUBLISH_DIR%
echo   - Build Command: %BUILD_COMMAND%

if "%PRODUCTION_DEPLOY%"=="true" (
    echo   - Type: Production
) else (
    echo   - Type: Preview
)

echo.
echo %BLUE%[DEPLOY]%NC% Next steps:
if "%PRODUCTION_DEPLOY%"=="false" (
    echo   - Test your preview deployment
    echo   - Deploy to production: %0 --prod
)
echo   - View deployment logs: netlify open
echo   - Manage site settings: netlify open:admin

echo.
echo %GREEN%[SUCCESS]%NC% 🌐 Check your Netlify dashboard for deployment details
