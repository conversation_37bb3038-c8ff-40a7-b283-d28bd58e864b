#!/usr/bin/env bash
set -euo pipefail

echo "🏗️ Building ChewyAI for production..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[BUILD]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check dependencies
print_status "Checking dependencies..."

if [ ! -d "node_modules" ]; then
    print_warning "Root dependencies not found. Installing..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    print_warning "Backend dependencies not found. Installing..."
    cd backend && npm install && cd ..
fi

if [ ! -d "frontend/node_modules" ]; then
    print_warning "Frontend dependencies not found. Installing..."
    cd frontend && npm install && cd ..
fi

print_success "Dependencies ready"

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf backend/dist
rm -rf frontend/dist

# Build backend
print_status "Building backend..."
cd backend
npm run build
if [ $? -eq 0 ]; then
    print_success "Backend build completed"
else
    print_error "Backend build failed"
    exit 1
fi
cd ..

# Build frontend
print_status "Building frontend..."
cd frontend
npm run build
if [ $? -eq 0 ]; then
    print_success "Frontend build completed"
else
    print_error "Frontend build failed"
    exit 1
fi
cd ..

# Verify builds
print_status "Verifying builds..."

if [ ! -f "backend/dist/index.cjs" ]; then
    print_error "Backend build verification failed - index.cjs not found"
    exit 1
fi

if [ ! -f "frontend/dist/index.html" ]; then
    print_error "Frontend build verification failed - index.html not found"
    exit 1
fi

print_success "Build verification passed"

# Display build info
print_status "Build Summary:"
echo "  - Backend: backend/dist/index.cjs ($(du -h backend/dist/index.cjs | cut -f1))"
echo "  - Frontend: frontend/dist/ ($(du -sh frontend/dist | cut -f1))"

print_success "🎉 Build completed successfully!"
print_status "To start production servers, run: bash scripts/production.sh"
