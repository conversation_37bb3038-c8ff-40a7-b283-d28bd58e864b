#!/usr/bin/env bash
set -euo pipefail

echo "⚙️ Setting up ChewyAI development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check system requirements
print_status "Checking system requirements..."

if ! command -v node >/dev/null 2>&1; then
    print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org/"
    exit 1
fi

if ! command -v npm >/dev/null 2>&1; then
    print_error "npm is not installed. Please install npm or use Node.js installer."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_warning "Node.js version is $NODE_VERSION. Recommended version is 18+."
fi

print_success "Node.js $(node --version) and npm $(npm --version) are available"

# Check for .env file
print_status "Checking environment configuration..."
if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        print_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        print_success "Created .env file from .env.example"
        print_warning "Please review and update .env file with your configuration"
    else
        print_warning "No .env or .env.example file found. You may need to create one manually."
    fi
else
    print_success ".env file exists"
fi

# Install dependencies
print_status "Installing dependencies..."

print_status "Installing root dependencies..."
npm install

print_status "Installing backend dependencies..."
cd backend
npm install
cd ..

print_status "Installing frontend dependencies..."
cd frontend
npm install
cd ..

print_success "All dependencies installed"

# Run type checking
print_status "Running type checks..."

print_status "Checking backend types..."
cd backend
npm run check
cd ..

print_status "Checking frontend types..."
cd frontend
npm run check
cd ..

print_success "Type checking passed"

# Display setup summary
print_status "Setup Summary:"
echo "  ✅ System requirements verified"
echo "  ✅ Dependencies installed"
echo "  ✅ Type checking passed"
echo "  ✅ Environment configured"

print_success "🎉 Setup completed successfully!"
echo ""
print_status "Next steps:"
echo "  1. Review and update .env file if needed"
echo "  2. Start development: npm run dev"
echo "  3. Or use: bash scripts/dev.sh"
echo ""
print_status "Available scripts:"
echo "  - bash scripts/dev.sh        # Start development servers"
echo "  - bash scripts/build.sh      # Build for production"
echo "  - bash scripts/production.sh # Start production servers"
echo "  - bash scripts/clean.sh      # Clean build artifacts"
