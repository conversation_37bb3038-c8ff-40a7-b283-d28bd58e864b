import React, { useState, useEffect } from "react";
import { Link, useLocation } from "wouter";
import { getAIProviderSettings } from "@/lib/ai-provider";
import { useAuth } from "../../hooks/useAuth";
import { useDocuments } from "../../hooks/useDocuments";
import { Tables } from "@/types/supabase";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import Spinner from "../ui/Spinner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteDocumentAPI } from "@/lib/api";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";

type StudyDocument = Tables<"study_documents">;

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const [location, navigate] = useLocation();
  const { user } = useAuth();
  const {
    data: documents = [],
    isLoading: loadingDocs,
    error,
  } = useDocuments();

  const aiSettings = getAIProviderSettings();

  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [documentToDelete, setDocumentToDelete] =
    useState<StudyDocument | null>(null);
  const [deletingDocumentIds, setDeletingDocumentIds] = useState<Set<string>>(
    new Set()
  );

  // Take only the first 10 documents and sort by creation date (newest first)
  const recentDocuments = documents
    .sort(
      (a: StudyDocument, b: StudyDocument) =>
        new Date(b.created_at || 0).getTime() -
        new Date(a.created_at || 0).getTime()
    )
    .slice(0, 10);

  const handleAddDocument = () => {
    navigate("/");
    if (onClose) onClose();
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent("openDocumentUploadDialog"));
    }, 100);
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete || !user) return;

    const previousDocuments = queryClient.getQueryData<StudyDocument[]>([
      "study-documents",
      user.id,
    ]);

    const docNameToToast = documentToDelete.file_name;
    const documentIdToDelete = documentToDelete.id;

    // Add to deleting set for immediate visual feedback
    setDeletingDocumentIds((prev) => new Set(prev.add(documentIdToDelete)));

    // Close the dialog and update UI immediately
    setDocumentToDelete(null);

    // Optimistically update the UI instantly
    queryClient.setQueryData<StudyDocument[] | undefined>(
      ["study-documents", user.id],
      (oldData) => oldData?.filter((doc) => doc.id !== documentIdToDelete)
    );

    // Show immediate success feedback
    toast({
      title: "Document Deleted",
      description: `"${docNameToToast}" has been deleted.`,
    });

    try {
      // Perform the actual deletion in the background
      await deleteDocumentAPI(documentIdToDelete);
    } catch (err: any) {
      // Show error and rollback optimistic update if API call failed
      toast({
        title: "Error Deleting Document",
        description: err.message || "Could not delete the document.",
        variant: "destructive",
      });
      console.error("Error deleting document:", err);

      // Rollback optimistic update
      if (previousDocuments) {
        queryClient.setQueryData(
          ["study-documents", user.id],
          previousDocuments
        );
      }

      // Show error-specific toast
      toast({
        title: "Document Restored",
        description: `"${docNameToToast}" has been restored due to deletion error.`,
        variant: "default",
      });
    } finally {
      // Remove from deleting set
      setDeletingDocumentIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentIdToDelete);
        return newSet;
      });
    }
  };

  const openDeleteConfirmation = (doc: StudyDocument) => {
    setDocumentToDelete(doc);
  };

  const NavLink: React.FC<{
    href: string;
    icon: string;
    label: string;
    exact?: boolean;
  }> = ({ href, icon, label, exact = false }) => {
    const isActive = exact ? location === href : location.startsWith(href);
    return (
      <li>
        <Link
          href={href}
          onClick={onClose}
          className={`flex items-center px-3 py-2.5 rounded-lg transition-colors duration-150 ease-in-out
            ${
              isActive
                ? "bg-purple-500/20 text-purple-400 font-medium"
                : "text-slate-300 hover:bg-slate-700 hover:text-purple-300"
            }`}
        >
          <span className="material-icons md-20 mr-3">{icon}</span>
          <span>{label}</span>
        </Link>
      </li>
    );
  };

  const SidebarContent = () => (
    <>
      <div className="mb-6">
        <h2 className="text-slate-400 text-xs uppercase font-semibold tracking-wider px-3 mb-2">
          Navigation
        </h2>
        <ul className="space-y-1">
          <NavLink href="/" icon="dashboard" label="Dashboard" exact={true} />
          <NavLink href="/flashcards" icon="style" label="Flashcards" />
          <NavLink href="/quizzes" icon="quiz" label="Quizzes" />
          <NavLink href="/faq" icon="help_outline" label="FAQ" />
        </ul>
      </div>

      <Separator className="my-6" />

      <div className="mb-6">
        <div className="flex justify-between items-center px-3 mb-2">
          <h2 className="text-slate-400 text-xs uppercase font-semibold tracking-wider">
            My Documents
          </h2>
          {loadingDocs && <Spinner size="sm" />}
        </div>
        {error ? (
          <div className="text-red-400 text-sm px-3 py-2">
            Error loading documents: {error.message}
          </div>
        ) : !loadingDocs && recentDocuments.length > 0 ? (
          <ul className="space-y-1 max-h-48 overflow-y-auto pr-1 my-documents-scrollbar">
            {recentDocuments
              .filter((doc: StudyDocument) => !deletingDocumentIds.has(doc.id))
              .map((doc: StudyDocument) => (
                <li key={doc.id}>
                  <Link
                    href={`/documents/${doc.id}`}
                    onClick={onClose}
                    className="flex items-center text-slate-300 px-3 py-2 rounded-lg hover:bg-slate-700 hover:text-purple-300 group"
                  >
                    <span className="material-icons md-20 mr-3 text-slate-400">
                      description
                    </span>
                    <span className="flex-1 truncate text-sm">
                      {doc.file_name}
                    </span>
                    <DropdownMenu>
                      <DropdownMenuTrigger
                        asChild
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Button
                          variant="ghost"
                          size="icon"
                          className="opacity-0 group-hover:opacity-100 text-slate-400 hover:bg-slate-700 hover:text-purple-300 ml-2 h-7 w-7"
                          onClick={(e) => {
                            e.preventDefault(); // Prevent navigation
                            e.stopPropagation(); // Stop event from bubbling to Link
                          }}
                        >
                          <span className="material-icons text-lg">
                            more_vert
                          </span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        align="end"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <DropdownMenuItem
                          onClick={(e) => {
                            e.stopPropagation();
                            openDeleteConfirmation(doc);
                          }}
                          className="text-red-500 hover:!text-red-500 hover:!bg-red-500/10"
                        >
                          <span className="material-icons text-base mr-2">
                            delete
                          </span>
                          Delete Document
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </Link>
                </li>
              ))}
          </ul>
        ) : !loadingDocs ? (
          <p className="text-slate-400 text-sm px-3 py-2">
            No documents found.
          </p>
        ) : null}
        <Button
          variant="ghost"
          className="mt-2 flex items-center text-purple-400 hover:text-purple-300 w-full justify-start px-3 py-2"
          onClick={handleAddDocument}
        >
          <span className="material-icons text-lg mr-2">
            add_circle_outline
          </span>
          <span className="text-sm font-medium">Add Document</span>
        </Button>
      </div>

      <Separator className="my-6" />

      <div>
        <h2 className="text-slate-400 text-xs uppercase font-semibold tracking-wider px-3 mb-2">
          AI Settings
        </h2>
        <div className="bg-slate-700 rounded-lg p-3 mx-1 border border-slate-600">
          <div className="mb-2.5">
            <p className="text-xs text-slate-400 mb-0.5">Current Model</p>
            <p className="text-sm font-medium text-slate-200 flex items-center">
              <span className="material-icons text-purple-400 text-base mr-1.5">
                smart_toy
              </span>
              {aiSettings.model}
            </p>
          </div>
          <div className="mb-3">
            <p className="text-xs text-slate-400 mb-0.5">API Provider</p>
            <p className="text-sm font-medium text-slate-200">
              {aiSettings.provider}
            </p>
          </div>
          <Link href="/ai-config" onClick={onClose} className="block">
            <Button
              variant="outline"
              size="sm"
              className="w-full text-sm border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-purple-300 hover:border-purple-400"
            >
              <span className="material-icons text-base mr-1.5">settings</span>
              <span>Configure AI</span>
            </Button>
          </Link>
        </div>
      </div>
    </>
  );

  // Mobile sidebar using Sheet component
  const MobileSidebarView = () => (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        side="left"
        className="w-[300px] sm:w-[320px] bg-slate-800 p-0 flex flex-col border-r border-slate-700"
      >
        <SheetHeader className="p-4 border-b border-slate-700">
          <SheetTitle className="flex items-center text-lg text-purple-400">
            <span className="material-icons mr-2 text-purple-400">
              psychology
            </span>
            ChewyAI
          </SheetTitle>
        </SheetHeader>
        <div className="p-4 overflow-y-auto flex-1 my-documents-scrollbar">
          <SidebarContent />
        </div>
      </SheetContent>
    </Sheet>
  );

  // Desktop sidebar
  const DesktopSidebarView = () => (
    <aside className="w-72 bg-slate-800 border-r border-slate-700 hidden md:flex flex-col h-full fixed top-0 left-0 pt-16">
      <nav className="p-4 overflow-y-auto flex-1 my-documents-scrollbar">
        <SidebarContent />
      </nav>
    </aside>
  );

  // Conditional rendering for SSR or to avoid hydration issues if any with Sheet
  const [hasMounted, setHasMounted] = useState(false);
  useEffect(() => {
    setHasMounted(true);
  }, []);

  if (!hasMounted) {
    return null;
  }

  return (
    <>
      <MobileSidebarView />
      <DesktopSidebarView />
      {documentToDelete && (
        <AlertDialog
          open={!!documentToDelete}
          onOpenChange={(open) => !open && setDocumentToDelete(null)}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                document "{documentToDelete.file_name}" and all associated data.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setDocumentToDelete(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteDocument}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
};

export default Sidebar;
