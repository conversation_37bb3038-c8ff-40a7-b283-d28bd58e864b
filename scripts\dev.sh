#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Starting ChewyAI in development mode..."
echo "This script starts both frontend (port 3000) and backend (port 5000) development servers"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[DEV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Please create one from .env.example if available"
fi

# Check dependencies
print_status "Checking dependencies..."

# Check root dependencies
if [ ! -d "node_modules" ]; then
    print_warning "Root dependencies not found. Installing..."
    npm install
fi

# Check backend dependencies
if [ ! -d "backend/node_modules" ]; then
    print_warning "Backend dependencies not found. Installing..."
    cd backend && npm install && cd ..
fi

# Check frontend dependencies
if [ ! -d "frontend/node_modules" ]; then
    print_warning "Frontend dependencies not found. Installing..."
    cd frontend && npm install && cd ..
fi

print_success "All dependencies ready"

# Set development environment
export NODE_ENV=development

print_status "Starting development servers..."
print_status "Frontend (Vite): http://localhost:3000"
print_status "Backend (Express): http://localhost:5000"
print_warning "Press Ctrl+C to stop all servers"

# Start both servers using the existing npm script
npm run dev
