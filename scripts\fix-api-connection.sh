#!/usr/bin/env bash
set -euo pipefail

echo "🔧 Quick Fix: ChewyAI API Connection Issues"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[FIX]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Diagnosing API connection issues..."

# Check if Netlify CLI is available
if ! command -v netlify >/dev/null 2>&1; then
    print_error "Netlify CLI is not installed. Please install it first:"
    echo "npm install -g netlify-cli"
    exit 1
fi

# Get site ID
SITE_ID=""
if [ -f ".netlify/state.json" ]; then
    SITE_ID=$(cat .netlify/state.json | grep -o '"siteId"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"siteId"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/' || echo "")
fi

if [ -z "$SITE_ID" ]; then
    print_error "No Netlify site found. Please run 'netlify link' first."
    exit 1
fi

print_status "Found Netlify site: $SITE_ID"

# Check current environment variables
print_status "Checking current Netlify environment variables..."
ENV_OUTPUT=$(netlify env:list --context production --plain 2>/dev/null || echo "")

if echo "$ENV_OUTPUT" | grep -q "VITE_API_BASE_URL"; then
    CURRENT_API_URL=$(echo "$ENV_OUTPUT" | grep "VITE_API_BASE_URL" | cut -d'=' -f2 | tr -d ' ')
    print_status "Current VITE_API_BASE_URL: $CURRENT_API_URL"
    
    if [[ "$CURRENT_API_URL" == *"localhost"* ]]; then
        print_error "❌ API URL is set to localhost - this won't work in production!"
    else
        print_success "✅ API URL is set to a remote server"
        
        # Test if the backend is accessible
        print_status "Testing backend connectivity..."
        BACKEND_URL=${CURRENT_API_URL%/api}
        
        if curl -s --max-time 10 "$BACKEND_URL/health" >/dev/null 2>&1; then
            print_success "✅ Backend is accessible at $BACKEND_URL"
            print_status "Your API connection should be working. If you're still having issues:"
            echo "1. Clear your browser cache"
            echo "2. Check browser console for specific errors"
            echo "3. Verify your backend CORS settings allow your Netlify domain"
            exit 0
        else
            print_error "❌ Backend is not accessible at $BACKEND_URL"
            print_warning "This is likely why your frontend can't connect to the API"
        fi
    fi
else
    print_error "❌ VITE_API_BASE_URL is not set in Netlify environment variables"
fi

# Offer solutions
echo ""
print_status "🔧 Available Solutions:"
echo ""
echo "1. 📋 I have a backend URL ready"
echo "2. 🚀 I need to deploy my backend first"
echo "3. 🔍 Show me current environment variables"
echo "4. ❌ Exit"
echo ""

read -p "Choose an option (1-4): " -n 1 -r
echo

case $REPLY in
    1)
        echo ""
        print_status "Great! Let's configure your backend URL."
        echo ""
        echo "Common backend platforms and their URL formats:"
        echo "- Railway: https://your-app.railway.app"
        echo "- Render: https://your-app.onrender.com"
        echo "- Heroku: https://your-app.herokuapp.com"
        echo "- Custom domain: https://api.yourdomain.com"
        echo ""
        read -p "Enter your backend URL (without /api): " BACKEND_URL
        
        if [ -z "$BACKEND_URL" ]; then
            print_error "Backend URL cannot be empty"
            exit 1
        fi
        
        # Remove trailing slash
        BACKEND_URL=${BACKEND_URL%/}
        
        # Test connectivity
        print_status "Testing backend connectivity..."
        if curl -s --max-time 10 "$BACKEND_URL/health" >/dev/null 2>&1; then
            print_success "✅ Backend is accessible!"
        else
            print_warning "⚠️ Backend test failed, but continuing with configuration..."
            print_warning "Make sure your backend is deployed and accessible"
        fi
        
        # Configure environment
        print_status "Configuring Netlify environment variables..."
        bash scripts/configure-env.sh --backend-url "$BACKEND_URL" --environment "production"
        
        if [ $? -eq 0 ]; then
            print_success "✅ Environment configured!"
            echo ""
            print_status "Next step: Redeploy your frontend"
            read -p "Deploy now? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                bash scripts/deploy.sh --prod
            else
                print_status "Run this when ready: bash scripts/deploy.sh --prod"
            fi
        fi
        ;;
    2)
        print_status "You need to deploy your backend first. Here are your options:"
        echo ""
        echo "📖 See BACKEND_DEPLOYMENT.md for detailed instructions"
        echo ""
        echo "Quick options:"
        echo "1. Railway (Recommended): npm install -g @railway/cli && railway login"
        echo "2. Render: Connect GitHub repo at render.com"
        echo "3. Heroku: heroku create your-app-name"
        echo ""
        print_status "After deploying your backend, run this script again with option 1"
        ;;
    3)
        print_status "Current Netlify environment variables:"
        echo ""
        print_status "Production context:"
        netlify env:list --context production --plain
        echo ""
        print_status "All contexts:"
        netlify env:list --plain
        ;;
    4)
        print_status "Exiting..."
        exit 0
        ;;
    *)
        print_error "Invalid option"
        exit 1
        ;;
esac
