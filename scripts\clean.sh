#!/usr/bin/env bash
set -euo pipefail

echo "🧹 Cleaning ChewyAI build artifacts and dependencies..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CLEAN]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory and change to project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

print_status "Project root: $PROJECT_ROOT"

# Parse command line arguments
CLEAN_DEPS=false
CLEAN_BUILDS=true
CLEAN_CACHE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --deps|--dependencies)
            CLEAN_DEPS=true
            shift
            ;;
        --cache)
            CLEAN_CACHE=true
            shift
            ;;
        --all)
            CLEAN_DEPS=true
            CLEAN_BUILDS=true
            CLEAN_CACHE=true
            shift
            ;;
        --builds-only)
            CLEAN_DEPS=false
            CLEAN_BUILDS=true
            CLEAN_CACHE=false
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --deps, --dependencies  Clean node_modules directories"
            echo "  --cache                 Clean npm cache and temp files"
            echo "  --builds-only          Clean only build artifacts (default)"
            echo "  --all                  Clean everything"
            echo "  -h, --help             Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Clean build artifacts
if [ "$CLEAN_BUILDS" = true ]; then
    print_status "Cleaning build artifacts..."
    
    if [ -d "backend/dist" ]; then
        print_status "Removing backend/dist/"
        rm -rf backend/dist
    fi
    
    if [ -d "frontend/dist" ]; then
        print_status "Removing frontend/dist/"
        rm -rf frontend/dist
    fi
    
    print_success "Build artifacts cleaned"
fi

# Clean dependencies
if [ "$CLEAN_DEPS" = true ]; then
    print_status "Cleaning dependencies..."
    
    if [ -d "node_modules" ]; then
        print_status "Removing root node_modules/"
        rm -rf node_modules
    fi
    
    if [ -d "backend/node_modules" ]; then
        print_status "Removing backend/node_modules/"
        rm -rf backend/node_modules
    fi
    
    if [ -d "frontend/node_modules" ]; then
        print_status "Removing frontend/node_modules/"
        rm -rf frontend/node_modules
    fi
    
    print_success "Dependencies cleaned"
fi

# Clean cache
if [ "$CLEAN_CACHE" = true ]; then
    print_status "Cleaning cache and temporary files..."
    
    # Clean npm cache
    npm cache clean --force 2>/dev/null || true
    
    # Clean common temp directories
    rm -rf .tmp
    rm -rf .cache
    rm -rf backend/.cache
    rm -rf frontend/.cache
    
    # Clean TypeScript build info
    rm -f tsconfig.tsbuildinfo
    rm -f backend/tsconfig.tsbuildinfo
    rm -f frontend/tsconfig.tsbuildinfo
    
    print_success "Cache and temporary files cleaned"
fi

# Display summary
print_status "Clean Summary:"
if [ "$CLEAN_BUILDS" = true ]; then
    echo "  ✅ Build artifacts removed"
fi
if [ "$CLEAN_DEPS" = true ]; then
    echo "  ✅ Dependencies removed"
fi
if [ "$CLEAN_CACHE" = true ]; then
    echo "  ✅ Cache and temp files removed"
fi

print_success "🎉 Cleaning completed!"

if [ "$CLEAN_DEPS" = true ]; then
    echo ""
    print_status "Next steps:"
    echo "  1. Reinstall dependencies: bash scripts/setup.sh"
    echo "  2. Or run: npm install && cd backend && npm install && cd ../frontend && npm install"
fi
