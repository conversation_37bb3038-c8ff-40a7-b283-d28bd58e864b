# Multi-stage build for ChewyAI
FROM node:18-alpine AS base
WORKDIR /app

# Install dependencies for both frontend and backend
FROM base AS deps
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY backend/package*.json ./backend/
RUN npm ci
RUN cd frontend && npm ci
RUN cd backend && npm ci

# Build frontend
FROM base AS frontend-builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules
COPY frontend ./frontend
COPY shared ./shared
RUN cd frontend && npm run build

# Build backend
FROM base AS backend-builder
COPY --from=deps /app/backend/node_modules ./backend/node_modules
COPY backend ./backend
COPY shared ./shared
RUN cd backend && npm run build

# Production image
FROM node:18-alpine AS runner
WORKDIR /app

# Copy built applications
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist
COPY --from=frontend-builder /app/frontend/server.js ./frontend/
COPY --from=backend-builder /app/backend/dist ./backend/dist
COPY --from=deps /app/backend/node_modules ./backend/node_modules
COPY --from=deps /app/frontend/node_modules ./frontend/node_modules

# Copy shared files
COPY shared ./shared
COPY package*.json ./

# Install production dependencies
RUN npm ci --only=production

# Create startup script
RUN echo '#!/bin/sh' > start.sh && \
    echo 'cd /app/backend && node dist/index.cjs &' >> start.sh && \
    echo 'cd /app/frontend && node server.js &' >> start.sh && \
    echo 'wait' >> start.sh && \
    chmod +x start.sh

EXPOSE 3000 5000

CMD ["./start.sh"]
