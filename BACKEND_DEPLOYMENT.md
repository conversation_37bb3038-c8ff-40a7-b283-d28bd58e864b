# ChewyAI Backend Deployment Guide

Your frontend is successfully deployed to Netlify, but you need to deploy your backend to a separate service. Here are the recommended options and steps.

## Quick Fix for Current Issue

### Option 1: Use Railway (Recommended)

1. **Install Railway CLI**:
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and Deploy**:
   ```bash
   railway login
   cd backend
   railway init
   railway up
   ```

3. **Get your Railway URL** (something like `https://your-app.railway.app`)

4. **Configure Netlify with your backend URL**:
   ```bash
   bash scripts/configure-env.sh --backend-url https://your-app.railway.app
   ```

5. **Redeploy frontend**:
   ```bash
   bash scripts/deploy.sh --prod
   ```

### Option 2: Use Render

1. **Connect GitHub Repository**:
   - Go to [render.com](https://render.com)
   - Connect your GitHub account
   - Create new "Web Service"
   - Select your repository
   - Set root directory to `backend`
   - Build command: `npm install && npm run build`
   - Start command: `npm start`

2. **Configure Environment Variables** in Render dashboard:
   - `NODE_ENV=production`
   - `PORT=10000` (Render default)
   - Add your Supabase and other environment variables

3. **Get your Render URL** and configure Netlify:
   ```bash
   bash scripts/configure-env.sh --backend-url https://your-app.onrender.com
   bash scripts/deploy.sh --prod
   ```

### Option 3: Use Heroku

1. **Install Heroku CLI** and login:
   ```bash
   heroku login
   ```

2. **Create and deploy**:
   ```bash
   cd backend
   heroku create your-app-name
   git subtree push --prefix=backend heroku main
   ```

3. **Configure and redeploy frontend**:
   ```bash
   bash scripts/configure-env.sh --backend-url https://your-app-name.herokuapp.com
   bash scripts/deploy.sh --prod
   ```

## Backend Environment Variables

Make sure your backend deployment includes these environment variables:

### Required Variables:
```bash
NODE_ENV=production
PORT=5000  # or whatever port your service uses
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### Optional Variables:
```bash
# AI Provider Configuration
DEFAULT_AI_PROVIDER=openrouter
DEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1
DEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash
DEFAULT_GENERATION_MODEL=google/gemini-2.5-pro

# CORS Configuration
FRONTEND_URL=https://your-netlify-site.netlify.app
```

## CORS Configuration

Your backend needs to allow requests from your Netlify domain. Update your backend CORS settings:

```javascript
// In your backend Express app
app.use(cors({
  origin: [
    'http://localhost:3000',  // Development
    'https://your-netlify-site.netlify.app',  // Production
    'https://deploy-preview-*--your-netlify-site.netlify.app'  // Preview deploys
  ],
  credentials: true
}));
```

## Testing Your Deployment

After deploying both frontend and backend:

1. **Test backend directly**:
   ```bash
   curl https://your-backend-url.com/health
   curl https://your-backend-url.com/api/health
   ```

2. **Test frontend API connection**:
   - Open your Netlify site
   - Check browser console for API_BASE_URL
   - Try logging in or any API operation

3. **Debug connection issues**:
   ```bash
   # Check Netlify environment variables
   netlify env:list --site your-site-id
   
   # Check backend logs
   # (varies by platform - Railway, Render, Heroku each have their own log viewers)
   ```

## Platform-Specific Instructions

### Railway Deployment

```bash
# In backend directory
railway init
railway add  # Add database if needed
railway up
railway domain  # Get your domain
```

**Environment Variables**: Set in Railway dashboard or via CLI:
```bash
railway variables set NODE_ENV=production
railway variables set SUPABASE_URL=your_url
```

### Render Deployment

1. Create `render.yaml` in backend directory:
```yaml
services:
  - type: web
    name: chewyai-backend
    env: node
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
```

### Heroku Deployment

1. Create `Procfile` in backend directory:
```
web: npm start
```

2. Set environment variables:
```bash
heroku config:set NODE_ENV=production
heroku config:set SUPABASE_URL=your_url
```

## Troubleshooting

### Common Issues:

1. **CORS Errors**:
   - Add your Netlify domain to backend CORS configuration
   - Check that backend is accessible from browser

2. **Environment Variables Not Set**:
   ```bash
   # Check Netlify env vars
   netlify env:list
   
   # Set missing variables
   netlify env:set VITE_API_BASE_URL https://your-backend.com/api
   ```

3. **Backend Not Responding**:
   - Check backend logs on your deployment platform
   - Verify backend is running and accessible
   - Test backend endpoints directly with curl

4. **Build Failures**:
   - Ensure all dependencies are in package.json
   - Check Node.js version compatibility
   - Verify build scripts work locally

### Debug Commands:

```bash
# Test backend health
curl -v https://your-backend-url.com/health

# Check Netlify build logs
netlify logs

# Test API from command line
curl -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"test"}' \
     https://your-backend-url.com/api/auth/login
```

## Next Steps

1. **Choose a backend deployment platform** (Railway recommended for simplicity)
2. **Deploy your backend** following the platform-specific instructions
3. **Configure Netlify environment variables** with your backend URL
4. **Redeploy your frontend** to pick up the new configuration
5. **Test the complete application** end-to-end

## Cost Considerations

- **Railway**: Free tier with usage limits, then pay-as-you-go
- **Render**: Free tier for static sites, $7/month for web services
- **Heroku**: No free tier, starts at $5/month for hobby dynos
- **Netlify**: Free tier for frontend hosting, functions have usage limits

Choose based on your budget and scaling needs.
